"use client";

import { useState } from "react";
import {
  Users,
  DollarSign,
  Alert<PERSON>riangle,
  Star,
  Activity
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  AreaChart,
  Area
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { MerchantOverviewApiService } from "@/lib/api/merchant-overview";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Mock data for Merchant Admin dashboard


const tellerPerformanceData = [
  { name: "John Smith", branch: "Downtown", transactions: 89, avgValue: 278, score: 94 },
  { name: "Sarah Johnson", branch: "Downtown", transactions: 76, avgValue: 312, score: 91 },
  { name: "Mike Brown", branch: "Uptown", transactions: 82, avgValue: 258, score: 88 },
  { name: "Lisa Davis", branch: "Mall", transactions: 95, avgValue: 273, score: 96 },
];

const customerBehaviorData = [
  { hour: "9AM", newCustomers: 12, returningCustomers: 45, avgTransaction: 245 },
  { hour: "11AM", newCustomers: 18, returningCustomers: 67, avgTransaction: 289 },
  { hour: "1PM", newCustomers: 25, returningCustomers: 89, avgTransaction: 312 },
  { hour: "3PM", newCustomers: 22, returningCustomers: 78, avgTransaction: 298 },
  { hour: "5PM", newCustomers: 15, returningCustomers: 56, avgTransaction: 267 },
];

const peakTransactionPeriods = [
  { day: "Monday", morning: 45, afternoon: 78, evening: 34 },
  { day: "Tuesday", morning: 52, afternoon: 82, evening: 38 },
  { day: "Wednesday", morning: 48, afternoon: 85, evening: 41 },
  { day: "Thursday", morning: 55, afternoon: 92, evening: 45 },
  { day: "Friday", morning: 62, afternoon: 105, evening: 67 },
  { day: "Saturday", morning: 38, afternoon: 95, evening: 78 },
  { day: "Sunday", morning: 25, afternoon: 67, evening: 45 },
];

const topProductsData = [
  { product: "Mobile Top-up", sales: 1250, revenue: 62500 },
  { product: "Bill Payment", sales: 890, revenue: 44500 },
  { product: "Money Transfer", sales: 650, revenue: 32500 },
  { product: "Account Opening", sales: 420, revenue: 21000 },
];

export default function MerchantAdminDashboard() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id, // Using user.id which is "MER-0091"
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform data for charts
  const chartData = overviewData ? MerchantOverviewApiService.transformToChartData(overviewData) : null;
  const kpis = overviewData ? MerchantOverviewApiService.calculateKPIs(overviewData) : null;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Merchant Admin Dashboard</h2>
        <p className="text-muted-foreground">
          All branches, tellers, and transactions under your management
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Customers"
          value={isLoading ? "..." : kpis?.totalCustomers.toString() || "0"}
          icon={Users}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Total Transactions"
          value={isLoading ? "..." : kpis?.totalTransactions.toString() || "0"}
          icon={Activity}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Total Revenue"
          value={isLoading ? "..." : formatCurrency(kpis?.totalRevenue || 0)}
          icon={DollarSign}
          trend={{ value: 15.3, isPositive: true }}
        />
        <KpiCard
          title="High-Value Customers"
          value={isLoading ? "..." : kpis?.highValueCustomers.toString() || "0"}
          icon={Star}
          trend={{ value: 6.8, isPositive: true }}
        />
      </div>

      {/* API Data Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Monthly Transaction Volume">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={overviewData?.transaction_volume.data ?
              overviewData.transaction_volume.data.labels.map((label, index) => ({
                month: label,
                volume: overviewData.transaction_volume.data.values[index]
              })) : []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Volume"]} />
              <Bar dataKey="volume" fill={COLORS[0]} name="Transaction Volume" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Monthly Transaction Count">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={overviewData?.transaction_count.data ?
              overviewData.transaction_count.data.labels.map((label, index) => ({
                month: label,
                count: overviewData.transaction_count.data.values[index]
              })) : []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="count" stroke={COLORS[1]} strokeWidth={3} name="Transaction Count" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Average Transaction Value & Customer Segmentation */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Average Transaction Value Trend">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={overviewData?.average_transactions.data ?
              overviewData.average_transactions.data.labels.map((label, index) => ({
                month: label,
                avgValue: overviewData.average_transactions.data.values[index]
              })) : []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Avg Value"]} />
              <Line type="monotone" dataKey="avgValue" stroke={COLORS[2]} strokeWidth={3} name="Average Value" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

      </div>

      {/* Mock Data Charts (No API Integration Yet) */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Behavior Analysis (Mock Data)">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={customerBehaviorData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area type="monotone" dataKey="newCustomers" stackId="1" stroke="#0088FE" fill="#0088FE" name="New Customers" />
              <Area type="monotone" dataKey="returningCustomers" stackId="1" stroke="#00C49F" fill="#00C49F" name="Returning Customers" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Peak Transaction Periods (Mock Data)">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={peakTransactionPeriods}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="morning" fill="#0088FE" name="Morning" />
              <Bar dataKey="afternoon" fill="#00C49F" name="Afternoon" />
              <Bar dataKey="evening" fill="#FFBB28" name="Evening" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Segmentation */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Segmentation (High, Mid, Low)">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData?.customerSegmentation || []}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name}: ${value}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {(chartData?.customerSegmentation || []).map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, `${name} Customers`]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

      </div>

      {/* Customer Transaction Frequency Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Transaction Frequency">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData?.transactionFrequency?.slice(0, 10) || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="customerId" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="averageDaysBetween" fill={COLORS[3]} name="Avg Days Between Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Segmentation Revenue Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData?.customerSegmentation || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "amount") return [formatCurrency(Number(value)), "Revenue"];
                return [value, name];
              }} />
              <Legend />
              <Bar dataKey="value" fill={COLORS[0]} name="Customer Count" />
              <Bar dataKey="amount" fill={COLORS[1]} name="Total Revenue" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* High-Value Customer Identification */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5" />
            <span>Top Customers by Amount</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <span className="ml-2">Loading customer data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-600">
              Error loading customer data: {error.message}
            </div>
          ) : (
            <DataTable
              title="Top Customers by Amount"
              columns={[
                { key: "customerId", title: "Customer ID" },
                { key: "formattedAmount", title: "Total Amount" },
                { key: "merchantId", title: "Merchant ID" },
              ]}
              data={chartData?.topCustomers || []}
            />
          )}
        </CardContent>
      </Card>

      {/* Transaction Outliers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Transaction Outliers</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <span className="ml-2">Loading outlier data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-600">
              Error loading outlier data: {error.message}
            </div>
          ) : chartData?.outliers && chartData.outliers.length > 0 ? (
            <div className="space-y-3">
              {chartData.outliers.map((outlier, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">Unusual transaction detected</div>
                    <div className="text-sm text-muted-foreground">
                      Customer {outlier.customer_id} - {formatCurrency(outlier.amount)}
                    </div>
                  </div>
                  <Badge variant="destructive">Outlier</Badge>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No transaction outliers detected in the selected period.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Debug Section - Only show in development */}
      {process.env.NODE_ENV === 'development' && overviewData && (
        <Card>
          <CardHeader>
            <CardTitle>API Response Debug (Development Only)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs">
              <p><strong>Merchant ID:</strong> {user.id}</p>
              <p><strong>Filter:</strong> {selectedPeriod}</p>
              <p><strong>API Params:</strong> {JSON.stringify(apiParams)}</p>
              <details className="mt-4">
                <summary className="cursor-pointer font-medium">Raw API Response</summary>
                <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto max-h-96">
                  {JSON.stringify(overviewData, null, 2)}
                </pre>
              </details>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
