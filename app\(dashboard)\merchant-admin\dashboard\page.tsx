"use client";

import { useState } from "react";
import {
  Users,
  DollarSign,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Star,
  Activity
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Pie<PERSON>hart,
  <PERSON>,
  Cell,
  Legend,
  Composed<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Utility functions for data transformation and error handling
const transformSegmentationData = (segmentation: any) => {
  if (!segmentation?.data) return [];

  const { high_value, mid_value, low_value } = segmentation.data;

  return [
    { name: "High Value", value: high_value?.length || 0, color: COLORS[0] },
    { name: "Mid Value", value: mid_value?.length || 0, color: COLORS[1] },
    { name: "Low Value", value: low_value?.length || 0, color: COLORS[2] }
  ];
};

const transformTransactionVolumeData = (transactionVolume: any) => {
  if (!transactionVolume?.data?.labels || !transactionVolume?.data?.values) return [];

  return transactionVolume.data.labels.map((label: string, index: number) => ({
    month: label,
    volume: transactionVolume.data.values[index] || 0
  }));
};

const transformTransactionCountData = (transactionCount: any) => {
  if (!transactionCount?.data?.labels || !transactionCount?.data?.values) return [];

  return transactionCount.data.labels.map((label: string, index: number) => ({
    month: label,
    count: transactionCount.data.values[index] || 0
  }));
};

const transformAverageTransactionData = (averageTransactions: any) => {
  if (!averageTransactions?.data?.labels || !averageTransactions?.data?.values) return [];

  return averageTransactions.data.labels.map((label: string, index: number) => ({
    month: label,
    avgValue: averageTransactions.data.values[index] || 0
  }));
};

const transformTopCustomersData = (topCustomers: any) => {
  if (!topCustomers?.data) return [];

  return topCustomers.data.map((customer: any, index: number) => ({
    id: customer.customer_id,
    customerId: customer.customer_id,
    amount: customer.amount,
    formattedAmount: formatCurrency(customer.amount),
    merchantId: customer.merchant_id,
    rank: index + 1
  }));
};

const transformOutliersData = (outliers: any) => {
  if (!outliers?.data) return [];

  return outliers.data
    .filter((item: any) => item.outlier)
    .map((outlier: any, index: number) => ({
      rank: index + 1,
      merchant_id: outlier.merchant_id,
      customer_id: outlier.customer_id,
      amount: outlier.amount,
      formattedAmount: formatCurrency(outlier.amount),
      isOutlier: outlier.outlier,
      outlierType: outlier.amount > 2000 ? 'High Value' : 'Low Value' // Simple classification
    }));
};

const transformDaysBetweenTransactionsData = (daysBetweenData: any) => {
  if (!daysBetweenData?.data) return [];

  // Group by customer and calculate average days between transactions
  const customerGroups = daysBetweenData.data.reduce((acc: any, item: any) => {
    if (!acc[item.customer_id]) {
      acc[item.customer_id] = [];
    }
    if (item.days_since !== null) {
      acc[item.customer_id].push(item.days_since);
    }
    return acc;
  }, {});

  return Object.entries(customerGroups).map(([customerId, days]: [string, any]) => {
    const avgDays = days.length > 0 ? days.reduce((sum: number, day: number) => sum + day, 0) / days.length : 0;
    return {
      customerId,
      averageDays: Math.round(avgDays * 10) / 10,
      transactionCount: days.length + 1 // +1 for the first transaction
    };
  }).sort((a, b) => a.averageDays - b.averageDays);
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine if we should show error or no data message
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    // Check if it's a 404 with "No data after filtering" message
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

// Helper function to get KPI value with proper error handling
const getKpiValue = (isLoading: boolean, error: any, value: number | string | undefined, fallback: string = "0") => {
  if (isLoading) return "...";
  if (error) {
    // Check if it's a 404 with "No data after filtering" message
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return fallback;
    }
    return "Error";
  }
  return value?.toString() || fallback;
};

export default function MerchantAdminDashboard() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id, // Using user.id which is "MER-0091"
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform API data using our utility functions
  const segmentationData = overviewData ? transformSegmentationData(overviewData.segmentation) : [];
  const transactionVolumeData = overviewData ? transformTransactionVolumeData(overviewData.transaction_volume) : [];
  const transactionCountData = overviewData ? transformTransactionCountData(overviewData.transaction_count) : [];
  const averageTransactionData = overviewData ? transformAverageTransactionData(overviewData.average_transactions) : [];
  const topCustomersData = overviewData ? transformTopCustomersData(overviewData.top_customers) : [];
  const outliersData = overviewData ? transformOutliersData(overviewData.transaction_outliers) : [];
  const daysBetweenData = overviewData ? transformDaysBetweenTransactionsData(overviewData.days_between_transactions) : [];

  // Extract stats for KPI cards using exact metric names from API
  const statsKpis = overviewData?.stats ? {
    totalTransactionValue: overviewData.stats.find(s => s.metric === 'Total Transaction Value for Last 30 Days')?.value || 0,
    averageTransactionValue: overviewData.stats.find(s => s.metric === 'Average Transaction Value for Last 30 Days')?.value || 0,
    transactionCount: overviewData.stats.find(s => s.metric === 'Transaction Count for Last 30 Days')?.value || 0,
    totalBranches: overviewData.stats.find(s => s.metric === 'Total Branches for Last 30 Days')?.value || 0,
    uniqueTerminals: overviewData.stats.find(s => s.metric === 'Unique Terminals for Last 30 Days')?.value || 0,
    minTransactionValue: overviewData.stats.find(s => s.metric === 'Min Transaction Value for Last 30 Days')?.value || 0,
    maxTransactionValue: overviewData.stats.find(s => s.metric === 'Max Transaction Value for Last 30 Days')?.value || 0,
  } : null;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Merchant Admin Dashboard</h2>
        <p className="text-muted-foreground">
          All branches, tellers, and transactions under your management
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert - Only show for actual errors, not "no data after filtering" */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert - Show for "no data after filtering" */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Transaction Value"
          value={(() => {
            const rawValue = getKpiValue(isLoading, error, statsKpis?.totalTransactionValue, "0");
            return rawValue === "..." || rawValue === "Error" || rawValue === "0" ? rawValue : formatCurrency(Number(rawValue));
          })()}
          icon={DollarSign}
          trend={{ value: 15.3, isPositive: true }}
        />
        <KpiCard
          title="Total Transactions"
          value={getKpiValue(isLoading, error, statsKpis?.transactionCount, "0")}
          icon={Activity}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Average Transaction Value"
          value={(() => {
            const rawValue = getKpiValue(isLoading, error, statsKpis?.averageTransactionValue, "0");
            return rawValue === "..." || rawValue === "Error" || rawValue === "0" ? rawValue : formatCurrency(Number(rawValue));
          })()}
          icon={Star}
          trend={{ value: 6.8, isPositive: true }}
        />
        <KpiCard
          title="Unique Terminals"
          value={getKpiValue(isLoading, error, statsKpis?.uniqueTerminals, "0")}
          icon={Users}
          trend={{ value: 12.5, isPositive: true }}
        />
      </div>

      {/* API Data Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Monthly Transaction Volume">
          {(() => {
            const state = getChartState(isLoading, error, transactionVolumeData.length > 0, "transaction volume data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={transactionVolumeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Volume"]} />
                    <Bar dataKey="volume" fill={COLORS[0]} name="Transaction Volume" />
                  </BarChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Monthly Transaction Count">
          {(() => {
            const state = getChartState(isLoading, error, transactionCountData.length > 0, "transaction count data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={transactionCountData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="count" stroke={COLORS[1]} strokeWidth={3} name="Transaction Count" />
                  </LineChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>
      </div>

      {/* Average Transaction Value & Customer Segmentation */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Average Transaction Value Trend">
          {(() => {
            const state = getChartState(isLoading, error, averageTransactionData.length > 0, "average transaction data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={averageTransactionData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Avg Value"]} />
                    <Line type="monotone" dataKey="avgValue" stroke={COLORS[2]} strokeWidth={3} name="Average Value" />
                  </LineChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Customer Segmentation Distribution">
          {(() => {
            const state = getChartState(isLoading, error, segmentationData.length > 0, "customer segmentation data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={segmentationData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {segmentationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, `${name} Customers`]} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>
      </div>

      {/* Transaction Frequency Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Transaction Frequency">
          {(() => {
            const state = getChartState(isLoading, error, daysBetweenData.length > 0, "transaction frequency data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={daysBetweenData.slice(0, 10)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="customerId"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      fontSize={12}
                    />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        `${value} days`,
                        name === "averageDays" ? "Avg Days Between Transactions" : name
                      ]}
                    />
                    <Bar dataKey="averageDays" fill={COLORS[3]} name="Avg Days Between Transactions" />
                  </BarChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Transaction Volume vs Count Comparison">
          {(() => {
            const hasData = transactionVolumeData.length > 0 && transactionCountData.length > 0;
            const state = getChartState(isLoading, error, hasData, "comparison data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={transactionVolumeData.map((item: any, index: number) => ({
                    ...item,
                    count: transactionCountData[index]?.count || 0
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === "volume" ? formatCurrency(Number(value)) : value,
                        name === "volume" ? "Transaction Volume" : "Transaction Count"
                      ]}
                    />
                    <Legend />
                    <Bar yAxisId="left" dataKey="volume" fill={COLORS[0]} name="Volume" />
                    <Line yAxisId="right" type="monotone" dataKey="count" stroke={COLORS[1]} strokeWidth={3} name="Count" />
                  </ComposedChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>
      </div>

      {/* Additional Analytics */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Transaction Analytics Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="grid gap-4 md:grid-cols-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-8 w-3/4" />
                  </div>
                ))}
              </div>
            ) : error ? (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {error.message?.includes('No data after filtering')
                    ? "No analytics data available for the selected time period"
                    : `Failed to load analytics summary: ${error.message}`
                  }
                </AlertDescription>
              </Alert>
            ) : (
              <div className="grid gap-4 md:grid-cols-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {segmentationData.reduce((sum, item) => sum + item.value, 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {segmentationData.find(item => item.name === "High Value")?.value || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">High-Value Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {outliersData.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Transaction Outliers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {daysBetweenData.length > 0 ?
                      Math.round(daysBetweenData.reduce((sum, item) => sum + item.averageDays, 0) / daysBetweenData.length * 10) / 10
                      : 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Days Between Transactions</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* High-Value Customer Identification */}
      <div>
        <div>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <span className="ml-2">Loading customer data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
                {error.message?.includes('No data after filtering')
                  ? "No customer data available for the selected time period"
                  : `Error loading customer data: ${error.message}`
                }
              </div>
            </div>
          ) : (
            <DataTable
              title="Top Customers by Amount"
              columns={[
                { key: "rank", title: "Rank" },
                { key: "customerId", title: "Customer ID" },
                { key: "formattedAmount", title: "Total Amount" },
                { key: "merchantId", title: "Merchant ID" },
              ]}
              data={topCustomersData}
            />
          )}
        </div>
      </div>

      {/* Transaction Outliers Table */}
      <div>
        <div>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <span className="ml-2">Loading outlier data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
                {error.message?.includes('No data after filtering')
                  ? "No transaction outliers detected for the selected time period"
                  : `Error loading outlier data: ${error.message}`
                }
              </div>
            </div>
          ) : outliersData.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No transaction outliers detected in the selected period.
            </div>
          ) : (
            <DataTable
              title="Transaction Outliers Analysis"
              columns={[
                { key: "rank", title: "Rank" },
                { key: "customer_id", title: "Customer ID" },
                { key: "merchant_id", title: "Merchant ID" },
                { key: "formattedAmount", title: "Transaction Amount" },
                {
                  key: "outlierType",
                  title: "Outlier Type",
                  render: (value) => (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      value === 'High Value'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {value}
                    </span>
                  )
                },
              ]}
              data={outliersData}
            />
          )}
        </div>
      </div>

      {/* Debug Section - Only show in development */}
      {process.env.NODE_ENV === 'development' && overviewData && (
        <Card>
          <CardHeader>
            <CardTitle>API Response Debug (Development Only)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs">
              <p><strong>Merchant ID:</strong> {user.id}</p>
              <p><strong>Filter:</strong> {selectedPeriod}</p>
              <p><strong>API Params:</strong> {JSON.stringify(apiParams)}</p>
              <details className="mt-4">
                <summary className="cursor-pointer font-medium">Raw API Response</summary>
                <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto max-h-96">
                  {JSON.stringify(overviewData, null, 2)}
                </pre>
              </details>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
